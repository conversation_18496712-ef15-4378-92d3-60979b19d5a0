# 🔧 Deno Deploy 环境变量设置指南

## 📍 在哪里设置环境变量

### 方法一：通过 Deno Deploy 控制台（推荐）

1. **访问项目页面**
   - 登录 https://dash.deno.com/
   - 找到你的项目（如 `openai-proxy`）
   - 点击项目名称进入项目详情

2. **进入设置页面**
   - 在项目页面顶部，点击 **"Settings"** 标签
   - 或者在左侧菜单中找到 **"Settings"** 选项

3. **添加环境变量**
   - 在 Settings 页面中，找到 **"Environment Variables"** 部分
   - 点击 **"Add Variable"** 或 **"+"** 按钮

4. **输入变量**
   ```
   变量名: ATLASSIAN_EMAIL
   变量值: <EMAIL>
   
   变量名: ATLASSIAN_TOKEN  
   变量值: ATATT3xFfGF0JViaOzqL71JfmA91fg0KDgKmPUcCH1AJUM8uBjH9G9AyYIdAH7re0v9224VVr0OfqdmguJluHPpRXPC-eIDHx5jEhrVzVkSQfvI69MiNKNq8awQCDUSwzAl0Zgf08bjjW2fTlTyA2ag_GkhjAK1OMjVEIrwak9CLM7Vf0Iinhl0=EFD21949
   ```

5. **保存设置**
   - 点击 **"Save"** 或 **"Add"** 按钮
   - 系统会自动重新部署项目

## 📱 详细步骤截图说明

### 步骤 1: 找到项目
```
Deno Deploy 首页 → Projects → 你的项目名称
```

### 步骤 2: 进入设置
```
项目页面 → Settings 标签 → Environment Variables 部分
```

### 步骤 3: 添加变量
```
点击 "Add Variable" → 输入名称和值 → 保存
```

## 🔄 方法二：通过 GitHub 集成（如果使用 GitHub 部署）

如果你是通过 GitHub 仓库部署的：

1. **在项目设置中**
   - 进入 Deno Deploy 项目页面
   - 点击 **"Settings"** 标签
   - 找到 **"Environment Variables"** 部分

2. **添加环境变量**（同方法一）

## ⚡ 必需的环境变量

### 主要凭据
```
ATLASSIAN_EMAIL=<EMAIL>
ATLASSIAN_TOKEN=ATATT3xFfGF0JViaOzqL71JfmA91fg0KDgKmPUcCH1AJUM8uBjH9G9AyYIdAH7re0v9224VVr0OfqdmguJluHPpRXPC-eIDHx5jEhrVzVkSQfvI69MiNKNq8awQCDUSwzAl0Zgf08bjjW2fTlTyA2ag_GkhjAK1OMjVEIrwak9CLM7Vf0Iinhl0=EFD21949
```

### 可选变量
```
DEBUG_MODE=false
```

### 备用凭据（可选）
```
ATLASSIAN_EMAIL_1=<EMAIL>
ATLASSIAN_TOKEN_1=backup-token
```

## 🔍 验证环境变量设置

### 1. 检查健康状态
```bash
curl https://your-project-name.deno.dev/health
```

成功响应示例：
```json
{
  "status": "ok",
  "timestamp": "2025-06-17T12:00:00.000Z",
  "models": 6,
  "credentials": 1
}
```

### 2. 测试模型列表
```bash
curl https://your-project-name.deno.dev/v1/models
```

### 3. 测试聊天功能
```bash
curl -X POST https://your-project-name.deno.dev/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "anthropic:claude-3-5-sonnet-v2@20241022",
    "messages": [{"role": "user", "content": "测试"}],
    "max_tokens": 10
  }'
```

## 🚨 常见问题

### Q: 设置环境变量后还是不工作？
**A**: 检查以下几点：
1. 变量名是否正确（区分大小写）
2. Token 是否完整（很长的字符串）
3. 是否点击了保存按钮
4. 等待重新部署完成（通常几秒钟）

### Q: 在哪里查看部署日志？
**A**: 
1. 进入项目页面
2. 点击 **"Deployments"** 标签
3. 点击最新的部署记录
4. 查看 **"Function Logs"** 部分

### Q: 如何知道环境变量是否生效？
**A**: 
1. 访问 `/health` 端点
2. 查看 `credentials` 字段是否大于 0
3. 如果是 0，说明环境变量未正确设置

## 📝 注意事项

1. **安全性**: 环境变量在 Deno Deploy 中是加密存储的
2. **重新部署**: 修改环境变量会触发自动重新部署
3. **生效时间**: 通常在保存后几秒钟内生效
4. **备份**: 建议记录你的环境变量设置

## 🎯 快速检查清单

- [ ] 登录 Deno Deploy 控制台
- [ ] 找到你的项目
- [ ] 进入 Settings → Environment Variables
- [ ] 添加 ATLASSIAN_EMAIL 变量
- [ ] 添加 ATLASSIAN_TOKEN 变量
- [ ] 保存设置
- [ ] 等待重新部署完成
- [ ] 测试 `/health` 端点
- [ ] 测试 `/v1/models` 端点

完成这些步骤后，你的 API 服务器就可以正常工作了！